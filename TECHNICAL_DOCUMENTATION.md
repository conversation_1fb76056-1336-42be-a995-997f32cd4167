# Revalu - Technical Documentation

## Database Schema Overview

### Core Entities (13 tables)
- **User & Authentication**: `User`
- **Property Data**: `Property`, `Valuation`, `Prediction`
- **Geographic**: `Suburb`, `MarketTrend`
- **Development**: `DevelopmentApplication`
- **Location Services**: `School`, `TransportStop`, `CrimeStatistic`
- **User Interactions**: `UserSearch`, `SavedProperty`, `Report`

### New Entities - Phase 1 (8 tables)
- **Alerts**: `Alert`, `AlertHistory`
- **Analysis**: `Comparison`, `Amalgamation`
- **Data Integration**: `DataSource`, `WebhookData`
- **Infrastructure**: `InfrastructureProject`
- **AI Engine**: `EventUpliftFactor`, `PropertyIntelligence`

## API Endpoints Reference

### Authentication
```
POST /auth/register
POST /auth/login
POST /auth/refresh
POST /auth/logout
```

### Properties
```
GET  /properties/search
GET  /properties/:id
GET  /properties/:id/intelligence
POST /properties/:id/track
```

### Development Analysis
```
POST /development/analyze
POST /development/simulate
```

### Amalgamation Analysis
```
GET  /amalgamation
POST /amalgamation
POST /amalgamation/analyze
PUT  /amalgamation/:id
DELETE /amalgamation/:id
```

### Market Intelligence
```
POST /market/heat-map
POST /market/trends
POST /market/emerging-suburbs
GET  /market/movers
GET  /market/heat-map-quick
GET  /market/trends-quick
```

### Alerts & Notifications
```
GET  /alerts
POST /alerts
GET  /alerts/summary
GET  /alerts/:id
PUT  /alerts/:id
DELETE /alerts/:id
POST /alerts/history
POST /alerts/trigger
```

### Webhooks (External Data Integration)
```
POST /webhooks/property-data
POST /webhooks/market-data
GET  /webhooks/data-sources/status
```

### Suburbs & Valuations
```
GET  /suburbs/search
GET  /suburbs/:id/profile
POST /valuations
GET  /valuations/:id
```

## WebSocket Events

### Property Namespace (`/property`)
```javascript
// Client → Server
'subscribe-property'   { propertyId: string }
'unsubscribe-property' { propertyId: string }

// Server → Client
'subscription-confirmed' { type, id, room }
'property-update'       { propertyId, timestamp, data }
'valuation-update'      { propertyId, timestamp, valuation }
'intelligence-update'   { propertyId, timestamp, intelligence }
'alert-triggered'       { propertyId, timestamp, alert }
```

### Market Namespace (`/market`)
```javascript
// Client → Server
'subscribe-suburb'        { suburbId: string }
'subscribe-market-movers' {}
'unsubscribe-suburb'      { suburbId: string }
'unsubscribe-market-movers' {}

// Server → Client
'subscription-confirmed'  { type, id?, room }
'market-trend-update'     { suburbId, timestamp, trend }
'infrastructure-update'   { suburbId, timestamp, infrastructure }
'development-update'      { suburbId, timestamp, development }
'market-movers-update'    { timestamp, data }
'market-alert'           { suburbId, timestamp, alert }
```

## Data Types & Enums

### Alert Types
```typescript
enum AlertType {
  VALUE_CHANGE = 'VALUE_CHANGE',
  NEW_LISTING = 'NEW_LISTING',
  PRICE_DROP = 'PRICE_DROP',
  DEVELOPMENT_APPLICATION = 'DEVELOPMENT_APPLICATION',
  INFRASTRUCTURE_UPDATE = 'INFRASTRUCTURE_UPDATE',
  SCHOOL_RATING_CHANGE = 'SCHOOL_RATING_CHANGE',
  MARKET_TREND = 'MARKET_TREND',
  RISK_ALERT = 'RISK_ALERT',
  CUSTOM = 'CUSTOM'
}
```

### Data Source Types (18 Sources)
```typescript
enum DataSourceType {
  PROPERTY_SALES = 'PROPERTY_SALES',
  CURRENT_LISTINGS = 'CURRENT_LISTINGS',
  PLANNING_ZONING = 'PLANNING_ZONING',
  DEVELOPMENT_APPLICATIONS = 'DEVELOPMENT_APPLICATIONS',
  ECONOMIC_INDICATORS = 'ECONOMIC_INDICATORS',
  INFRASTRUCTURE_PROJECTS = 'INFRASTRUCTURE_PROJECTS',
  SCHOOL_PERFORMANCE = 'SCHOOL_PERFORMANCE',
  CRIME_STATISTICS = 'CRIME_STATISTICS',
  RENTAL_MARKET = 'RENTAL_MARKET',
  TRANSPORT_ACCESSIBILITY = 'TRANSPORT_ACCESSIBILITY',
  CLIMATE_RISK = 'CLIMATE_RISK',
  DEMOGRAPHICS = 'DEMOGRAPHICS',
  CONSTRUCTION_COSTS = 'CONSTRUCTION_COSTS',
  MARKET_SENTIMENT = 'MARKET_SENTIMENT',
  ENERGY_EFFICIENCY = 'ENERGY_EFFICIENCY',
  MORTGAGE_STRESS = 'MORTGAGE_STRESS',
  BUSINESS_ACTIVITY = 'BUSINESS_ACTIVITY',
  SOCIAL_TRENDS = 'SOCIAL_TRENDS'
}
```

### Heat Map Types
```typescript
enum HeatMapType {
  PRICE_GROWTH = 'price_growth',
  DEVELOPMENT_ACTIVITY = 'development_activity',
  YIELDS = 'yields',
  INVESTMENT_SCORE = 'investment_score'
}
```

## Event-Uplift Engine™ Implementation

### Factor Storage
```typescript
interface EventUpliftFactor {
  propertyId: string;
  factorType: string;    // 'infrastructure', 'school_rating', etc.
  factorName: string;    // Specific factor name
  impact: number;        // Impact percentage (-1 to 1)
  confidence: number;    // Confidence score (0-1)
  timeHorizon: number;   // Months into future
  source: string;        // Data source
  metadata?: any;        // Additional factor data
}
```

### Intelligence Scores
```typescript
interface PropertyIntelligence {
  propertyId: string;
  investmentScore?: number;      // 0-100
  developmentScore?: number;     // 0-100
  growthPotential?: number;      // Growth percentage
  riskScore?: number;           // 0-100
  lifestyleScore?: number;      // 0-100
  marketPosition?: number;      // Percentile in suburb
  confidenceScore?: number;     // Overall confidence (0-1)
  keyInsights: string[];        // Array of insights
  opportunities?: any;          // Opportunities data
  risks?: any;                 // Risk assessment data
}
```

## Webhook Data Integration

### Property Data Webhook
```typescript
interface PropertyDataWebhook {
  source: string;
  sourceType: DataSourceType;
  eventType: string;
  propertyIdentifier: string;
  data: {
    address?: string;
    estimatedValue?: number;
    confidence?: number;
    upliftFactors?: EventUpliftFactor[];
    investmentScore?: number;
    developmentScore?: number;
    // ... additional property data
  };
  timestamp: string;
}
```

### Market Data Webhook
```typescript
interface MarketDataWebhook {
  source: string;
  sourceType: DataSourceType;
  eventType: string;
  areaIdentifier: string;
  data: {
    medianPrice?: number;
    priceChange?: number;
    infrastructureProjects?: InfrastructureProject[];
    developmentApplications?: DevelopmentApplication[];
    schoolUpdates?: SchoolUpdate[];
    // ... additional market data
  };
  timestamp: string;
}
```

## Development Commands

### Database Operations
```bash
# Generate Prisma client
cd packages/database && pnpm prisma generate

# Push schema changes
pnpm prisma db push

# Seed database
pnpm db:seed

# Reset database
pnpm prisma db push --force-reset
```

### Development Servers
```bash
# Start all services
pnpm dev

# Start API only
cd apps/api && pnpm dev

# Start web only
cd apps/web && pnpm dev

# Build all
pnpm build

# Build API only
cd apps/api && pnpm build
```

### Testing
```bash
# Run all tests
pnpm test

# Run API tests
cd apps/api && pnpm test

# Run with coverage
pnpm test:cov
```

## Environment Variables

### API (.env)
```bash
DATABASE_URL="postgresql://..."
JWT_SECRET="your-jwt-secret"
JWT_EXPIRES_IN="7d"
FRONTEND_URL="http://localhost:3000"
PORT=3001
```

### Web (.env.local)
```bash
NEXT_PUBLIC_API_URL="http://localhost:3001"
NEXT_PUBLIC_WS_URL="ws://localhost:3001"
```

## Performance Targets

- **Search Results**: < 3 seconds
- **Page Loads**: < 2 seconds
- **WebSocket Latency**: < 100ms
- **API Response Time**: < 500ms
- **Database Queries**: < 200ms

## Security Considerations

- JWT authentication with refresh tokens
- Rate limiting on all endpoints
- Input validation with class-validator
- SQL injection protection via Prisma
- CORS configuration
- Helmet.js security headers

## Monitoring & Logging

- Structured logging with Winston
- Error tracking with Sentry (planned)
- Performance monitoring (planned)
- Database query monitoring
- WebSocket connection tracking

---

*This documentation is updated as features are implemented.*
