// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// ENUMS
// ============================================================================

enum PropertyType {
  HOUSE
  APARTMENT
  TOWNHOUSE
  UNIT
  VILLA
  DUPLEX
  STUDIO
  LAND
  COMMERCIAL
  INDUSTRIAL
  OTHER
}

enum PropertyStatus {
  FOR_SALE
  SOLD
  FOR_RENT
  RENTED
  OFF_MARKET
  UNDER_CONTRACT
  AUCTION
}

enum ValuationMethod {
  AUTOMATED
  MANUAL
  HYBRID
  COMPARATIVE_MARKET_ANALYSIS
  COST_APPROACH
  INCOME_APPROACH
}

enum ValuationStatus {
  PENDING
  COMPLETED
  FAILED
  EXPIRED
}

enum PredictionModel {
  LINEAR_REGRESSION
  RANDOM_FOREST
  GRADIENT_BOOSTING
  NEURAL_NETWORK
  ENSEMBLE
}

enum TrendDirection {
  UP
  DOWN
  STABLE
}

enum TrendPeriod {
  WEEKLY
  MONTHLY
  QUARTERLY
  YEARLY
}

enum ApplicationStatus {
  SUBMITTED
  UNDER_REVIEW
  APPROVED
  REJECTED
  WITHDRAWN
}

enum ApplicationType {
  DEVELOPMENT
  SUBDIVISION
  DEMOLITION
  RENOVATION
  CHANGE_OF_USE
  OTHER
}

enum SchoolType {
  PRIMARY
  SECONDARY
  COMBINED
  SPECIAL
  PRIVATE
  PUBLIC
}

enum TransportType {
  BUS
  TRAIN
  TRAM
  FERRY
  METRO
  LIGHT_RAIL
}

enum CrimeType {
  THEFT
  ASSAULT
  BURGLARY
  VANDALISM
  DRUG_RELATED
  TRAFFIC
  DOMESTIC_VIOLENCE
  OTHER
}

enum ReportType {
  PROPERTY_VALUATION
  MARKET_ANALYSIS
  SUBURB_REPORT
  INVESTMENT_ANALYSIS
  COMPARATIVE_ANALYSIS
}

enum ReportStatus {
  GENERATING
  COMPLETED
  FAILED
  EXPIRED
}

enum AlertType {
  VALUE_CHANGE
  NEW_LISTING
  PRICE_DROP
  DEVELOPMENT_APPLICATION
  INFRASTRUCTURE_UPDATE
  SCHOOL_RATING_CHANGE
  MARKET_TREND
  RISK_ALERT
  CUSTOM
}

enum DataSourceType {
  PROPERTY_SALES
  CURRENT_LISTINGS
  PLANNING_ZONING
  DEVELOPMENT_APPLICATIONS
  ECONOMIC_INDICATORS
  INFRASTRUCTURE_PROJECTS
  SCHOOL_PERFORMANCE
  CRIME_STATISTICS
  RENTAL_MARKET
  TRANSPORT_ACCESSIBILITY
  CLIMATE_RISK
  DEMOGRAPHICS
  CONSTRUCTION_COSTS
  MARKET_SENTIMENT
  ENERGY_EFFICIENCY
  MORTGAGE_STRESS
  BUSINESS_ACTIVITY
  SOCIAL_TRENDS
}

enum DataSourceStatus {
  ACTIVE
  INACTIVE
  ERROR
  MAINTENANCE
}

enum InfrastructureType {
  TRANSPORT
  EDUCATION
  HEALTHCARE
  RETAIL
  RECREATION
  UTILITIES
  GOVERNMENT
}

enum InfrastructureStatus {
  PROPOSED
  APPROVED
  UNDER_CONSTRUCTION
  COMPLETED
  CANCELLED
}

// ============================================================================
// USER MANAGEMENT
// ============================================================================

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  firstName String?
  lastName  String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  searches        UserSearch[]
  savedProperties SavedProperty[]
  reports         Report[]
  alerts          Alert[]
  comparisons     Comparison[]
  amalgamations   Amalgamation[]

  @@map("users")
}

// ============================================================================
// LOCATION & GEOGRAPHY
// ============================================================================

model Suburb {
  id          String  @id @default(cuid())
  name        String
  state       String
  postcode    String
  country     String  @default("Australia")
  latitude    Float?
  longitude   Float?
  population  Int?
  area        Float? // in square kilometers
  medianAge   Float?
  medianPrice Float?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  properties            Property[]
  marketTrends          MarketTrend[]
  developmentApps       DevelopmentApplication[]
  schools               School[]
  transportStops        TransportStop[]
  crimeStatistics       CrimeStatistic[]
  alerts                Alert[]
  webhookData           WebhookData[]
  infrastructureProjects InfrastructureProject[]

  @@unique([name, state, postcode])
  @@index([state, postcode])
  @@index([name])
  @@map("suburbs")
}

// ============================================================================
// PROPERTIES
// ============================================================================

model Property {
  id              String        @id @default(cuid())
  address         String
  suburbId        String
  propertyType    PropertyType
  status          PropertyStatus @default(OFF_MARKET)
  bedrooms        Int?
  bathrooms       Int?
  carSpaces       Int?
  landSize        Float? // in square meters
  buildingSize    Float? // in square meters
  yearBuilt       Int?
  latitude        Float?
  longitude       Float?
  description     String?
  features        String[] // JSON array of features
  images          String[] // Array of image URLs
  lastSalePrice   Float?
  lastSaleDate    DateTime?
  currentPrice    Float?
  priceHistory    Json? // Array of price changes
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  suburb              Suburb @relation(fields: [suburbId], references: [id])
  valuations          Valuation[]
  predictions         Prediction[]
  savedByUsers        SavedProperty[]
  reports             Report[]
  alerts              Alert[]
  webhookData         WebhookData[]
  eventUpliftFactors  EventUpliftFactor[]
  intelligence        PropertyIntelligence?

  @@index([suburbId])
  @@index([propertyType])
  @@index([status])
  @@index([bedrooms, bathrooms])
  @@index([lastSalePrice])
  @@index([latitude, longitude])
  @@map("properties")
}

// ============================================================================
// VALUATIONS & PREDICTIONS
// ============================================================================

model Valuation {
  id              String           @id @default(cuid())
  propertyId      String
  estimatedValue  Float
  confidence      Float? // 0-1 confidence score
  method          ValuationMethod
  status          ValuationStatus  @default(PENDING)
  valuationDate   DateTime         @default(now())
  expiryDate      DateTime?
  factors         Json? // Factors that influenced the valuation
  comparables     Json? // Comparable properties used
  notes           String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  // Relationships
  property        Property @relation(fields: [propertyId], references: [id])

  @@index([propertyId])
  @@index([valuationDate])
  @@index([status])
  @@index([estimatedValue])
  @@map("valuations")
}

model Prediction {
  id              String          @id @default(cuid())
  propertyId      String
  predictedValue  Float
  confidence      Float? // 0-1 confidence score
  model           PredictionModel
  features        Json // Features used for prediction
  predictionDate  DateTime        @default(now())
  targetDate      DateTime? // Future date for prediction
  accuracy        Float? // Actual vs predicted (if known)
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // Relationships
  property        Property @relation(fields: [propertyId], references: [id])

  @@index([propertyId])
  @@index([predictionDate])
  @@index([model])
  @@index([predictedValue])
  @@map("predictions")
}

// ============================================================================
// MARKET DATA
// ============================================================================

model MarketTrend {
  id              String         @id @default(cuid())
  suburbId        String
  period          TrendPeriod
  direction       TrendDirection
  percentageChange Float
  averagePrice    Float?
  medianPrice     Float?
  salesVolume     Int?
  daysOnMarket    Float?
  startDate       DateTime
  endDate         DateTime
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Relationships
  suburb          Suburb @relation(fields: [suburbId], references: [id])

  @@index([suburbId])
  @@index([period])
  @@index([startDate, endDate])
  @@index([direction])
  @@map("market_trends")
}

// ============================================================================
// DEVELOPMENT & PLANNING
// ============================================================================

model DevelopmentApplication {
  id              String            @id @default(cuid())
  applicationId   String            @unique // Council application ID
  suburbId        String
  address         String
  applicationType ApplicationType
  status          ApplicationStatus
  description     String?
  estimatedValue  Float?
  submissionDate  DateTime
  decisionDate    DateTime?
  completionDate  DateTime?
  documents       String[] // Array of document URLs
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relationships
  suburb          Suburb @relation(fields: [suburbId], references: [id])

  @@index([suburbId])
  @@index([status])
  @@index([applicationType])
  @@index([submissionDate])
  @@map("development_applications")
}

// ============================================================================
// AMENITIES & INFRASTRUCTURE
// ============================================================================

model School {
  id          String     @id @default(cuid())
  name        String
  suburbId    String
  schoolType  SchoolType
  address     String?
  latitude    Float?
  longitude   Float?
  website     String?
  phone       String?
  rating      Float? // 0-10 rating
  enrollment  Int?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relationships
  suburb      Suburb @relation(fields: [suburbId], references: [id])

  @@index([suburbId])
  @@index([schoolType])
  @@index([rating])
  @@map("schools")
}

model TransportStop {
  id            String        @id @default(cuid())
  name          String
  suburbId      String
  transportType TransportType
  address       String?
  latitude      Float?
  longitude     Float?
  routes        String[] // Array of route names/numbers
  accessibility Boolean       @default(false)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relationships
  suburb        Suburb @relation(fields: [suburbId], references: [id])

  @@index([suburbId])
  @@index([transportType])
  @@index([latitude, longitude])
  @@map("transport_stops")
}

// ============================================================================
// CRIME & SAFETY
// ============================================================================

model CrimeStatistic {
  id          String    @id @default(cuid())
  suburbId    String
  crimeType   CrimeType
  incidents   Int
  period      String // e.g., "2024-Q1", "2024-01"
  year        Int
  month       Int?
  quarter     Int?
  rate        Float? // Per 1000 residents
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relationships
  suburb      Suburb @relation(fields: [suburbId], references: [id])

  @@index([suburbId])
  @@index([crimeType])
  @@index([year, month])
  @@index([year, quarter])
  @@map("crime_statistics")
}

// ============================================================================
// USER INTERACTIONS
// ============================================================================

model UserSearch {
  id              String   @id @default(cuid())
  userId          String
  query           String
  filters         Json? // Search filters applied
  resultsCount    Int?
  clickedResults  String[] // Array of property IDs clicked
  searchDate      DateTime @default(now())
  createdAt       DateTime @default(now())

  // Relationships
  user            User @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([searchDate])
  @@map("user_searches")
}

model SavedProperty {
  id          String   @id @default(cuid())
  userId      String
  propertyId  String
  notes       String?
  tags        String[] // User-defined tags
  savedAt     DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  user        User @relation(fields: [userId], references: [id])
  property    Property @relation(fields: [propertyId], references: [id])

  @@unique([userId, propertyId])
  @@index([userId])
  @@index([propertyId])
  @@map("saved_properties")
}

// ============================================================================
// REPORTS & ANALYTICS
// ============================================================================

model Report {
  id              String       @id @default(cuid())
  userId          String
  propertyId      String?
  reportType      ReportType
  status          ReportStatus @default(GENERATING)
  title           String
  parameters      Json // Report generation parameters
  content         Json? // Generated report content
  fileUrl         String? // URL to generated PDF/document
  generatedAt     DateTime?
  expiresAt       DateTime?
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  // Relationships
  user            User @relation(fields: [userId], references: [id])
  property        Property? @relation(fields: [propertyId], references: [id])

  @@index([userId])
  @@index([propertyId])
  @@index([reportType])
  @@index([status])
  @@index([createdAt])
  @@map("reports")
}

// ============================================================================
// ALERTS & NOTIFICATIONS
// ============================================================================

model Alert {
  id          String      @id @default(cuid())
  userId      String
  propertyId  String?
  suburbId    String?
  name        String
  type        AlertType
  conditions  Json        // Alert conditions and thresholds
  isActive    Boolean     @default(true)
  lastTriggered DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relationships
  user     User      @relation(fields: [userId], references: [id])
  property Property? @relation(fields: [propertyId], references: [id])
  suburb   Suburb?   @relation(fields: [suburbId], references: [id])
  history  AlertHistory[]

  @@index([userId])
  @@index([propertyId])
  @@index([suburbId])
  @@index([type])
  @@index([isActive])
  @@map("alerts")
}

model AlertHistory {
  id          String    @id @default(cuid())
  alertId     String
  triggeredAt DateTime  @default(now())
  data        Json      // Data that triggered the alert
  notified    Boolean   @default(false)
  notifiedAt  DateTime?

  // Relationships
  alert Alert @relation(fields: [alertId], references: [id], onDelete: Cascade)

  @@index([alertId])
  @@index([triggeredAt])
  @@map("alert_history")
}

// ============================================================================
// PROPERTY COMPARISONS
// ============================================================================

model Comparison {
  id          String   @id @default(cuid())
  userId      String
  name        String?
  propertyIds String[] // Array of property IDs
  analysis    Json?    // Comparison analysis results
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id])

  @@index([userId])
  @@map("comparisons")
}

// ============================================================================
// AMALGAMATION ANALYSIS
// ============================================================================

model Amalgamation {
  id              String   @id @default(cuid())
  userId          String
  name            String
  propertyIds     String[] // Array of property IDs to amalgamate
  totalLandSize   Float?   // Combined land size
  totalValue      Float?   // Combined current value
  developmentPlan Json?    // Development analysis and plans
  feasibility     Json?    // Financial feasibility analysis
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id])

  @@index([userId])
  @@map("amalgamations")
}

// ============================================================================
// DATA SOURCES & INTEGRATION
// ============================================================================

model DataSource {
  id          String           @id @default(cuid())
  name        String           @unique
  type        DataSourceType
  status      DataSourceStatus @default(ACTIVE)
  endpoint    String?          // API endpoint or data source URL
  lastSync    DateTime?
  confidence  Float?           // 0-1 confidence score
  metadata    Json?            // Additional source metadata
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Relationships
  webhookData WebhookData[]

  @@index([type])
  @@index([status])
  @@index([lastSync])
  @@map("data_sources")
}

model WebhookData {
  id           String     @id @default(cuid())
  dataSourceId String
  propertyId   String?
  suburbId     String?
  eventType    String     // Type of data event
  payload      Json       // Raw webhook payload
  processed    Boolean    @default(false)
  processedAt  DateTime?
  createdAt    DateTime   @default(now())

  // Relationships
  dataSource DataSource @relation(fields: [dataSourceId], references: [id])
  property   Property?  @relation(fields: [propertyId], references: [id])
  suburb     Suburb?    @relation(fields: [suburbId], references: [id])

  @@index([dataSourceId])
  @@index([propertyId])
  @@index([suburbId])
  @@index([processed])
  @@index([createdAt])
  @@map("webhook_data")
}

// ============================================================================
// INFRASTRUCTURE & DEVELOPMENT
// ============================================================================

model InfrastructureProject {
  id          String              @id @default(cuid())
  name        String
  type        InfrastructureType
  status      InfrastructureStatus
  description String?
  suburbId    String
  latitude    Float?
  longitude   Float?
  startDate   DateTime?
  endDate     DateTime?
  budget      Float?              // Project budget
  impact      Json?               // Expected impact analysis
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt

  // Relationships
  suburb Suburb @relation(fields: [suburbId], references: [id])

  @@index([suburbId])
  @@index([type])
  @@index([status])
  @@index([startDate])
  @@map("infrastructure_projects")
}

// ============================================================================
// EVENT-UPLIFT ENGINE™ ALGORITHM
// ============================================================================

model EventUpliftFactor {
  id          String   @id @default(cuid())
  propertyId  String
  factorType  String   // e.g., 'infrastructure', 'school_rating', 'crime_reduction'
  factorName  String   // Specific factor name
  impact      Float    // Impact percentage (-1 to 1)
  confidence  Float    // Confidence score (0-1)
  timeHorizon Int      // Months into future
  source      String   // Data source
  metadata    Json?    // Additional factor data
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  property Property @relation(fields: [propertyId], references: [id])

  @@index([propertyId])
  @@index([factorType])
  @@index([timeHorizon])
  @@map("event_uplift_factors")
}

model PropertyIntelligence {
  id                    String   @id @default(cuid())
  propertyId            String   @unique
  investmentScore       Float?   // 0-100 investment score
  developmentScore      Float?   // 0-100 development potential score
  growthPotential       Float?   // Growth percentage
  riskScore            Float?   // 0-100 risk score
  lifestyleScore       Float?   // 0-100 lifestyle score
  marketPosition       Float?   // Percentile in suburb
  confidenceScore      Float?   // Overall confidence (0-1)
  keyInsights          String[] // Array of key insights
  opportunities        Json?    // Opportunities data
  risks               Json?    // Risk assessment data
  lastCalculated      DateTime @default(now())
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relationships
  property Property @relation(fields: [propertyId], references: [id])

  @@index([investmentScore])
  @@index([developmentScore])
  @@index([lastCalculated])
  @@map("property_intelligence")
}
