import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../common/database/prisma.service';
import {
  buildPropertySearchWhere,
  createPaginationOptions,
  createSearchResult,
  calculateDistance,
} from '@revalu/database';
import {
  CreatePropertyDto,
  UpdatePropertyDto,
  PropertySearchDto,
  PropertyResponseDto,
  PropertyStatsDto,
  NearbyAmenitiesDto,
} from './dto/property.dto';
import { PaginatedResponseDto } from '../../common/dto/pagination.dto';

@Injectable()
export class PropertiesService {
  private readonly logger = new Logger(PropertiesService.name);

  constructor(private prisma: PrismaService) {}

  async create(createPropertyDto: CreatePropertyDto): Promise<PropertyResponseDto> {
    const { suburbId, ...propertyData } = createPropertyDto;

    // Verify suburb exists
    const suburb = await this.prisma.client.suburb.findUnique({
      where: { id: suburbId },
    });

    if (!suburb) {
      throw new BadRequestException('Suburb not found');
    }

    try {
      const property = await this.prisma.client.property.create({
        data: {
          ...propertyData,
          suburbId,
          lastSaleDate: propertyData.lastSaleDate
            ? new Date(propertyData.lastSaleDate)
            : undefined,
        },
        include: {
          suburb: {
            select: {
              id: true,
              name: true,
              state: true,
              postcode: true,
            },
          },
        },
      });

      this.logger.log(`Property created: ${property.id} at ${property.address}`);
      return this.transformPropertyResponse(property);
    } catch (error) {
      this.logger.error('Failed to create property:', error);
      throw new BadRequestException('Failed to create property');
    }
  }

  async findAll(searchDto: PropertySearchDto): Promise<PaginatedResponseDto<PropertyResponseDto>> {
    const { page, limit, sortBy, sortOrder, ...filters } = searchDto;
    const pagination = createPaginationOptions({ page, limit });
    const where = buildPropertySearchWhere(filters);

    // Build order by clause
    const orderBy: any = {};
    if (sortBy) {
      orderBy[sortBy] = sortOrder || 'desc';
    } else {
      orderBy.createdAt = 'desc';
    }

    try {
      const [properties, total] = await Promise.all([
        this.prisma.client.property.findMany({
          where,
          include: {
            suburb: {
              select: {
                id: true,
                name: true,
                state: true,
                postcode: true,
              },
            },
          },
          orderBy,
          skip: pagination.skip,
          take: pagination.take,
        }),
        this.prisma.client.property.count({ where }),
      ]);

      const transformedProperties = properties.map(this.transformPropertyResponse);

      return {
        success: true,
        data: transformedProperties,
        meta: {
          total,
          page: pagination.page,
          limit: pagination.limit,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page * pagination.limit < total,
          hasPrev: pagination.page > 1,
        },
        message: 'Properties retrieved successfully',
      };
    } catch (error) {
      this.logger.error('Failed to fetch properties:', error);
      throw new BadRequestException('Failed to fetch properties');
    }
  }

  async findOne(id: string): Promise<PropertyResponseDto> {
    const property = await this.prisma.client.property.findUnique({
      where: { id },
      include: {
        suburb: {
          select: {
            id: true,
            name: true,
            state: true,
            postcode: true,
          },
        },
        valuations: {
          orderBy: { valuationDate: 'desc' },
          take: 5,
        },
        predictions: {
          orderBy: { predictionDate: 'desc' },
          take: 3,
        },
      },
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    return this.transformPropertyResponse(property);
  }

  async update(id: string, updatePropertyDto: UpdatePropertyDto): Promise<PropertyResponseDto> {
    // Check if property exists
    const existingProperty = await this.prisma.client.property.findUnique({
      where: { id },
    });

    if (!existingProperty) {
      throw new NotFoundException('Property not found');
    }

    try {
      const property = await this.prisma.client.property.update({
        where: { id },
        data: updatePropertyDto,
        include: {
          suburb: {
            select: {
              id: true,
              name: true,
              state: true,
              postcode: true,
            },
          },
        },
      });

      this.logger.log(`Property updated: ${property.id}`);
      return this.transformPropertyResponse(property);
    } catch (error) {
      this.logger.error(`Failed to update property ${id}:`, error);
      throw new BadRequestException('Failed to update property');
    }
  }

  async remove(id: string): Promise<{ message: string }> {
    // Check if property exists
    const existingProperty = await this.prisma.client.property.findUnique({
      where: { id },
    });

    if (!existingProperty) {
      throw new NotFoundException('Property not found');
    }

    try {
      await this.prisma.client.property.delete({
        where: { id },
      });

      this.logger.log(`Property deleted: ${id}`);
      return { message: 'Property deleted successfully' };
    } catch (error) {
      this.logger.error(`Failed to delete property ${id}:`, error);
      throw new BadRequestException('Failed to delete property');
    }
  }

  async getStatistics(): Promise<PropertyStatsDto> {
    try {
      const [
        totalProperties,
        priceStats,
        typeStats,
        statusStats,
        bedroomStats,
      ] = await Promise.all([
        this.prisma.client.property.count(),
        this.prisma.client.property.aggregate({
          _avg: { lastSalePrice: true },
          _min: { lastSalePrice: true },
          _max: { lastSalePrice: true },
        }),
        this.prisma.client.property.groupBy({
          by: ['propertyType'],
          _count: true,
        }),
        this.prisma.client.property.groupBy({
          by: ['status'],
          _count: true,
        }),
        this.prisma.client.property.groupBy({
          by: ['bedrooms'],
          _count: true,
        }),
      ]);

      // Calculate median price
      const medianPrice = await this.calculateMedianPrice();

      return {
        totalProperties,
        averagePrice: priceStats._avg.lastSalePrice || 0,
        medianPrice,
        priceRange: {
          min: priceStats._min.lastSalePrice || 0,
          max: priceStats._max.lastSalePrice || 0,
        },
        byType: typeStats.reduce((acc, item) => {
          acc[item.propertyType] = item._count;
          return acc;
        }, {}),
        byStatus: statusStats.reduce((acc, item) => {
          acc[item.status] = item._count;
          return acc;
        }, {}),
        byBedrooms: bedroomStats.reduce((acc, item) => {
          acc[item.bedrooms?.toString() || 'unknown'] = item._count;
          return acc;
        }, {}),
      };
    } catch (error) {
      this.logger.error('Failed to get property statistics:', error);
      throw new BadRequestException('Failed to get property statistics');
    }
  }

  async getNearbyAmenities(id: string): Promise<NearbyAmenitiesDto> {
    const property = await this.prisma.client.property.findUnique({
      where: { id },
      include: { suburb: true },
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    if (!property.latitude || !property.longitude) {
      throw new BadRequestException('Property coordinates not available');
    }

    try {
      const [schools, transportStops] = await Promise.all([
        this.prisma.client.school.findMany({
          where: { suburbId: property.suburbId },
          include: { suburb: true },
        }),
        this.prisma.client.transportStop.findMany({
          where: { suburbId: property.suburbId },
          include: { suburb: true },
        }),
      ]);

      // Calculate distances and sort by proximity
      const nearbySchools = schools
        .map((school) => ({
          id: school.id,
          name: school.name,
          type: school.schoolType,
          distance: school.latitude && school.longitude
            ? calculateDistance(
                property.latitude!,
                property.longitude!,
                school.latitude,
                school.longitude,
              )
            : null,
          rating: school.rating,
        }))
        .filter((school) => school.distance !== null)
        .sort((a, b) => a.distance! - b.distance!)
        .slice(0, 10);

      const nearbyTransport = transportStops
        .map((stop) => ({
          id: stop.id,
          name: stop.name,
          type: stop.transportType,
          distance: stop.latitude && stop.longitude
            ? calculateDistance(
                property.latitude!,
                property.longitude!,
                stop.latitude,
                stop.longitude,
              )
            : null,
          routes: stop.routes,
        }))
        .filter((stop) => stop.distance !== null)
        .sort((a, b) => a.distance! - b.distance!)
        .slice(0, 10);

      return {
        propertyId: property.id,
        schools: nearbySchools,
        transport: nearbyTransport,
        distanceToCBD: this.calculateDistanceToCBD(property.latitude, property.longitude),
      };
    } catch (error) {
      this.logger.error(`Failed to get nearby amenities for property ${id}:`, error);
      throw new BadRequestException('Failed to get nearby amenities');
    }
  }

  private async calculateMedianPrice(): Promise<number> {
    const properties = await this.prisma.client.property.findMany({
      where: {
        lastSalePrice: { not: null },
      },
      select: { lastSalePrice: true },
      orderBy: { lastSalePrice: 'asc' },
    });

    if (properties.length === 0) return 0;

    const prices = properties.map(p => p.lastSalePrice!);
    const middle = Math.floor(prices.length / 2);

    if (prices.length % 2 === 0) {
      return (prices[middle - 1] + prices[middle]) / 2;
    } else {
      return prices[middle];
    }
  }

  private calculateDistanceToCBD(lat: number, lng: number): number {
    // Melbourne CBD coordinates as default
    const cbdLat = -37.8136;
    const cbdLng = 144.9631;
    return calculateDistance(lat, lng, cbdLat, cbdLng);
  }

  private transformPropertyResponse(property: any): PropertyResponseDto {
    return {
      id: property.id,
      address: property.address,
      propertyType: property.propertyType,
      status: property.status,
      bedrooms: property.bedrooms,
      bathrooms: property.bathrooms,
      carSpaces: property.carSpaces,
      landSize: property.landSize,
      buildingSize: property.buildingSize,
      yearBuilt: property.yearBuilt,
      latitude: property.latitude,
      longitude: property.longitude,
      description: property.description,
      features: property.features,
      images: property.images,
      currentPrice: property.currentPrice,
      lastSalePrice: property.lastSalePrice,
      lastSaleDate: property.lastSaleDate,
      createdAt: property.createdAt,
      updatedAt: property.updatedAt,
      suburb: property.suburb,
    };
  }
}
