'use client';

import { apiClient } from '@/lib/api';
import { useAuth, withAuth } from '@/lib/auth';
import { formatCurrency } from '@/lib/utils';
import { useQuery } from '@tanstack/react-query';
import {
    Activity,
    AlertTriangle,
    BarChart3,
    Bell,
    Building,
    Calculator,
    Eye,
    FileText,
    Home,
    TrendingDown,
    TrendingUp,
    Zap
} from 'lucide-react';

function DashboardPage() {
  const { user, logout } = useAuth();

  const { data: propertyStats, isLoading: statsLoading } = useQuery({
    queryKey: ['property-statistics'],
    queryFn: () => apiClient.getPropertyStatistics(),
  });

  const { data: recentProperties, isLoading: propertiesLoading } = useQuery({
    queryKey: ['recent-properties'],
    queryFn: () => apiClient.getProperties({ limit: 6, sortBy: 'createdAt', sortOrder: 'desc' }),
  });

  // Mock data for advanced dashboard features
  const mockDashboardData = {
    portfolio: {
      totalValue: 3850000,
      monthlyChange: { amount: 125000, percentage: 3.4 },
      properties: [
        { id: '1', address: '123 Brunswick St, New Farm', value: 1275000, change: 2.1, status: 'tracked' },
        { id: '2', address: '45 James St, Fortitude Valley', value: 950000, change: -1.2, status: 'tracked' },
        { id: '3', address: '78 Ann St, Brisbane City', value: 1625000, change: 4.8, status: 'tracked' }
      ],
      bestPerformer: { address: '78 Ann St, Brisbane City', change: 4.8 },
      worstPerformer: { address: '45 James St, Fortitude Valley', change: -1.2 },
      activeAlerts: 7
    },
    marketMovers: {
      growing: [
        { name: 'New Farm', state: 'QLD', growth: 8.5, median: 1150000 },
        { name: 'Teneriffe', state: 'QLD', growth: 7.2, median: 980000 },
        { name: 'Kangaroo Point', state: 'QLD', growth: 6.8, median: 750000 },
        { name: 'West End', state: 'QLD', growth: 6.1, median: 820000 },
        { name: 'Paddington', state: 'QLD', growth: 5.9, median: 1050000 }
      ],
      declining: [
        { name: 'Surfers Paradise', state: 'QLD', growth: -2.1, median: 650000 },
        { name: 'Broadbeach', state: 'QLD', growth: -1.8, median: 720000 },
        { name: 'Main Beach', state: 'QLD', growth: -1.5, median: 890000 }
      ]
    },
    insights: [
      {
        type: 'opportunity',
        title: 'New Infrastructure Project',
        description: 'Light rail extension near your New Farm property could increase value by 15-20%',
        impact: 'high',
        timeAgo: '2 hours ago',
        icon: 'zap'
      },
      {
        type: 'alert',
        title: 'Price Alert Triggered',
        description: 'Property at 45 James St dropped below your $960k threshold',
        impact: 'medium',
        timeAgo: '5 hours ago',
        icon: 'bell'
      },
      {
        type: 'insight',
        title: 'Market Analysis',
        description: 'Inner Brisbane suburbs showing strong growth momentum this quarter',
        impact: 'low',
        timeAgo: '1 day ago',
        icon: 'activity'
      },
      {
        type: 'risk',
        title: 'Market Risk Update',
        description: 'Interest rate changes may affect property values in Q2 2025',
        impact: 'medium',
        timeAgo: '2 days ago',
        icon: 'alert'
      }
    ]
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-primary-600">Revalu</h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="/properties" className="text-gray-700 hover:text-primary-600 transition-colors">
                Properties
              </a>
              <a href="/suburbs" className="text-gray-700 hover:text-primary-600 transition-colors">
                Suburbs
              </a>
              <a href="/valuations" className="text-gray-700 hover:text-primary-600 transition-colors">
                Valuations
              </a>
            </nav>
            <div className="flex items-center space-x-4">
              <a href="/tracked-properties" className="text-gray-700 hover:text-primary-600 transition-colors">
                Tracked
              </a>
              <span className="text-gray-700">
                Welcome, {user?.firstName || user?.email}
              </span>
              <button
                onClick={logout}
                className="text-gray-700 hover:text-primary-600 transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user?.firstName || 'User'}!
          </h2>
          <p className="text-gray-600">
            Here's your property intelligence dashboard with real-time insights and market updates.
          </p>
        </div>

        {/* Portfolio Summary Widget */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-900">Portfolio Summary</h3>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600">Live</span>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Total Portfolio Value */}
            <div className="lg:col-span-2">
              <div className="text-center lg:text-left">
                <div className="text-5xl font-bold text-gray-900 mb-2">
                  {formatCurrency(mockDashboardData.portfolio.totalValue)}
                </div>
                <div className="text-lg text-gray-600 mb-4">
                  Total Portfolio Value
                </div>
                <div className="flex items-center justify-center lg:justify-start space-x-4 mb-4">
                  <div className="flex items-center text-green-600">
                    <TrendingUp className="w-5 h-5 mr-1" />
                    <span className="font-semibold">
                      +{formatCurrency(mockDashboardData.portfolio.monthlyChange.amount)} (+{mockDashboardData.portfolio.monthlyChange.percentage}%)
                    </span>
                  </div>
                  <span className="text-gray-500">this month</span>
                </div>
                <div className="text-sm text-gray-600">
                  {mockDashboardData.portfolio.properties.length} tracked properties • {mockDashboardData.portfolio.activeAlerts} active alerts
                </div>
              </div>
            </div>

            {/* Performance Summary */}
            <div className="space-y-4">
              <div className="bg-green-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                  <TrendingUp className="w-4 h-4 mr-2 text-green-600" />
                  Best Performer
                </h4>
                <p className="text-sm text-gray-700 mb-1">{mockDashboardData.portfolio.bestPerformer.address}</p>
                <p className="text-lg font-bold text-green-600">+{mockDashboardData.portfolio.bestPerformer.change}%</p>
              </div>

              <div className="bg-red-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                  <TrendingDown className="w-4 h-4 mr-2 text-red-600" />
                  Needs Attention
                </h4>
                <p className="text-sm text-gray-700 mb-1">{mockDashboardData.portfolio.worstPerformer.address}</p>
                <p className="text-lg font-bold text-red-600">{mockDashboardData.portfolio.worstPerformer.change}%</p>
              </div>

              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                  <Bell className="w-4 h-4 mr-2 text-blue-600" />
                  Active Alerts
                </h4>
                <p className="text-2xl font-bold text-blue-600">{mockDashboardData.portfolio.activeAlerts}</p>
                <p className="text-sm text-gray-600">Price & market alerts</p>
              </div>
            </div>
          </div>

          {/* Portfolio Properties */}
          <div className="mt-8">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Tracked Properties</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {mockDashboardData.portfolio.properties.map((property, index) => (
                <div key={property.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-gray-900 text-sm">{property.address}</h5>
                    <span className={`text-sm font-bold ${property.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {property.change >= 0 ? '+' : ''}{property.change}%
                    </span>
                  </div>
                  <p className="text-lg font-bold text-gray-900 mb-2">{formatCurrency(property.value)}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">Tracked</span>
                    <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">View Details</button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Market Movers & Personal Insights */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Market Movers Widget */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900">Market Movers</h3>
              <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">View All</button>
            </div>

            <div className="space-y-6">
              {/* Top Growing Suburbs */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
                  Top Growing Suburbs
                </h4>
                <div className="space-y-3">
                  {mockDashboardData.marketMovers.growing.slice(0, 3).map((suburb, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{suburb.name}, {suburb.state}</p>
                        <p className="text-sm text-gray-600">{formatCurrency(suburb.median)} median</p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold text-green-600">+{suburb.growth}%</p>
                        <button className="text-xs text-blue-600 hover:text-blue-800">+ Watch</button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Declining Suburbs */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <TrendingDown className="w-5 h-5 mr-2 text-red-600" />
                  Watch List
                </h4>
                <div className="space-y-3">
                  {mockDashboardData.marketMovers.declining.slice(0, 2).map((suburb, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{suburb.name}, {suburb.state}</p>
                        <p className="text-sm text-gray-600">{formatCurrency(suburb.median)} median</p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold text-red-600">{suburb.growth}%</p>
                        <button className="text-xs text-blue-600 hover:text-blue-800">+ Watch</button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Personal Insights Feed */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900">Personal Insights</h3>
              <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">View All</button>
            </div>

            <div className="space-y-4">
              {mockDashboardData.insights.map((insight, index) => (
                <div key={index} className={`p-4 rounded-lg border-l-4 ${
                  insight.type === 'opportunity' ? 'bg-green-50 border-green-500' :
                  insight.type === 'alert' ? 'bg-yellow-50 border-yellow-500' :
                  insight.type === 'risk' ? 'bg-red-50 border-red-500' :
                  'bg-blue-50 border-blue-500'
                }`}>
                  <div className="flex items-start space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      insight.type === 'opportunity' ? 'bg-green-100' :
                      insight.type === 'alert' ? 'bg-yellow-100' :
                      insight.type === 'risk' ? 'bg-red-100' :
                      'bg-blue-100'
                    }`}>
                      {insight.icon === 'zap' && <Zap className="w-4 h-4 text-green-600" />}
                      {insight.icon === 'bell' && <Bell className="w-4 h-4 text-yellow-600" />}
                      {insight.icon === 'activity' && <Activity className="w-4 h-4 text-blue-600" />}
                      {insight.icon === 'alert' && <AlertTriangle className="w-4 h-4 text-red-600" />}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-semibold text-gray-900">{insight.title}</h4>
                        <span className="text-xs text-gray-500">{insight.timeAgo}</span>
                      </div>
                      <p className="text-sm text-gray-700">{insight.description}</p>
                      <div className="mt-2">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          insight.impact === 'high' ? 'bg-red-100 text-red-800' :
                          insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {insight.impact} impact
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions Center */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-900">Quick Actions</h3>
            <p className="text-gray-600">Get started with these common tasks</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <a
              href="/properties"
              className="group p-6 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 hover:shadow-md"
            >
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
                <Home className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Search Properties</h4>
              <p className="text-sm text-gray-600">Find and analyze properties with AI insights</p>
            </a>

            <a
              href="/tracked-properties"
              className="group p-6 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-all duration-200 hover:shadow-md"
            >
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-green-200 transition-colors">
                <Eye className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">View Portfolio</h4>
              <p className="text-sm text-gray-600">Monitor your tracked properties and alerts</p>
            </a>

            <button className="group p-6 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 hover:shadow-md text-left">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-purple-200 transition-colors">
                <FileText className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Generate Report</h4>
              <p className="text-sm text-gray-600">Create detailed property analysis reports</p>
            </button>

            <a
              href="/suburbs"
              className="group p-6 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-all duration-200 hover:shadow-md"
            >
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-orange-200 transition-colors">
                <BarChart3 className="w-6 h-6 text-orange-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Market Insights</h4>
              <p className="text-sm text-gray-600">Explore suburb trends and market data</p>
            </a>
          </div>

          {/* Additional Quick Actions */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Advanced Tools</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <Building className="w-5 h-5 text-blue-600" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Development Analysis</p>
                  <p className="text-sm text-gray-600">Analyze development potential</p>
                </div>
              </button>

              <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <Bell className="w-5 h-5 text-yellow-600" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Set Alert</p>
                  <p className="text-sm text-gray-600">Create price or market alerts</p>
                </div>
              </button>

              <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <Calculator className="w-5 h-5 text-green-600" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Get Valuation</p>
                  <p className="text-sm text-gray-600">AI-powered property valuation</p>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default withAuth(DashboardPage);
