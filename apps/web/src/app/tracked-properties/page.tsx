'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Target, Trash2, MapPin, TrendingUp, Home, Bell, DollarSign, Calendar, Activity } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth, withAuth } from '@/lib/auth';
import { apiClient } from '@/lib/api';
import { formatCurrency, formatPropertyFeatures, formatPropertyType } from '@/lib/utils';
import { Loading, LoadingCard } from '@/components/ui/loading';

// Helper function to generate mock tracking metrics
const generateTrackingMetrics = (property: any) => {
  const trackedDays = Math.floor(Math.random() * 180) + 30; // 30-210 days
  const originalValue = property.currentPrice || property.lastSalePrice || 1000000;
  const currentValue = originalValue * (1 + (Math.random() * 0.2 - 0.1)); // ±10% change
  const valueChange = currentValue - originalValue;
  const percentChange = ((valueChange / originalValue) * 100);
  const alertsSet = Math.floor(Math.random() * 4) + 1; // 1-4 alerts
  
  return {
    trackedSince: new Date(Date.now() - trackedDays * 24 * 60 * 60 * 1000),
    originalValue,
    currentValue,
    valueChange,
    percentChange,
    alertsSet,
    trackedDays,
  };
};

function TrackedPropertiesPage() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Fetch tracked properties
  const { data: trackedProperties, isLoading, error } = useQuery({
    queryKey: ['tracked-properties'],
    queryFn: () => apiClient.getTrackedProperties(),
  });

  // Remove property mutation
  const removePropertyMutation = useMutation({
    mutationFn: (propertyId: string) => apiClient.untrackProperty(propertyId),
    onSuccess: () => {
      toast.success('Property removed from tracking');
      queryClient.invalidateQueries({ queryKey: ['tracked-properties'] });
    },
    onError: () => {
      toast.error('Failed to remove property');
    },
  });

  const handleRemoveProperty = (propertyId: string) => {
    if (window.confirm('Are you sure you want to stop tracking this property?')) {
      removePropertyMutation.mutate(propertyId);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <LoadingCard key={i} />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Tracked Properties</h1>
              <p className="text-gray-600 mt-1">
                Monitor value changes and market trends for your tracked properties
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/properties" className="btn-outline">
                Browse Properties
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-600">Failed to load tracked properties. Please try again.</p>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && trackedProperties && trackedProperties.length === 0 && (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Target className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No tracked properties yet
            </h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Start tracking properties to monitor their value changes, market trends, and receive alerts. 
              Your tracked properties will appear here with detailed analytics.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/properties" className="btn-primary">
                <Home className="w-4 h-4 mr-2" />
                Browse Properties
              </Link>
              <Link href="/suburbs" className="btn-outline">
                Explore Suburbs
              </Link>
            </div>
          </div>
        )}

        {/* Properties Grid */}
        {trackedProperties && trackedProperties.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {trackedProperties.map((property: any) => {
              const metrics = generateTrackingMetrics(property);
              
              return (
                <div key={property.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                  {/* Property Image */}
                  <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                    {property.images?.[0] ? (
                      <img
                        src={property.images[0]}
                        alt={property.address}
                        className="w-full h-48 object-cover"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                        <MapPin className="w-12 h-12 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* Property Details */}
                  <div className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <Link href={`/properties/${property.id}`}>
                          <h3 className="text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors cursor-pointer truncate">
                            {property.address}
                          </h3>
                        </Link>
                        <p className="text-gray-600 text-sm">
                          {formatPropertyFeatures(property.bedrooms, property.bathrooms, property.carSpaces)}
                        </p>
                      </div>
                      <button
                        onClick={() => handleRemoveProperty(property.id)}
                        className="text-gray-400 hover:text-red-500 transition-colors ml-2"
                        title="Stop tracking"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>

                    {/* Tracking Metrics */}
                    <div className="space-y-3 mb-4">
                      {/* Value Change */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Current Value</span>
                        <div className="text-right">
                          <div className="font-semibold text-gray-900">
                            {formatCurrency(metrics.currentValue)}
                          </div>
                          <div className={`text-xs flex items-center ${
                            metrics.percentChange >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            <TrendingUp className={`w-3 h-3 mr-1 ${
                              metrics.percentChange < 0 ? 'rotate-180' : ''
                            }`} />
                            {metrics.percentChange >= 0 ? '+' : ''}{metrics.percentChange.toFixed(1)}%
                          </div>
                        </div>
                      </div>

                      {/* Value Change Amount */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Change Since Tracked</span>
                        <span className={`text-sm font-medium ${
                          metrics.valueChange >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {metrics.valueChange >= 0 ? '+' : ''}{formatCurrency(Math.abs(metrics.valueChange))}
                        </span>
                      </div>

                      {/* Tracking Duration */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          Tracked Since
                        </span>
                        <span className="text-sm text-gray-900">
                          {metrics.trackedSince.toLocaleDateString()}
                        </span>
                      </div>

                      {/* Alerts Set */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 flex items-center">
                          <Bell className="w-3 h-3 mr-1" />
                          Active Alerts
                        </span>
                        <span className="text-sm text-gray-900">
                          {metrics.alertsSet}
                        </span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <Link 
                        href={`/properties/${property.id}`}
                        className="flex-1 btn-outline btn-sm text-center"
                      >
                        <Activity className="w-4 h-4 mr-1" />
                        View Analysis
                      </Link>
                      <button className="btn-primary btn-sm">
                        <Bell className="w-4 h-4 mr-1" />
                        Alerts
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

export default withAuth(TrackedPropertiesPage);
