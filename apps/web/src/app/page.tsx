'use client';

import { PropertyCard } from '@/components/ui/property-card';
import { useAuth } from '@/lib/auth';
import { BarChart3, Calculator, Filter, MapPin, Search, Shield, Sparkles, TrendingUp, Users } from 'lucide-react';
import Link from 'next/link';
import { useRef, useState } from 'react';

export default function HomePage() {
  const { isAuthenticated, user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Mock suggestions for autocomplete
  const mockSuggestions = [
    'Brisbane CBD, QLD',
    'Gold Coast, QLD',
    'Surfers Paradise, QLD',
    'Broadbeach, QLD',
    'South Bank, QLD',
    'Fortitude Valley, QLD',
    'New Farm, QLD',
    'Kangaroo Point, QLD',
  ];

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      window.location.href = `/properties?search=${encodeURIComponent(searchQuery.trim())}`;
    }
  };

  const handleInputChange = (value: string) => {
    setSearchQuery(value);
    if (value.length > 2) {
      const filtered = mockSuggestions.filter(s =>
        s.toLowerCase().includes(value.toLowerCase())
      );
      setSuggestions(filtered.slice(0, 5));
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion);
    setShowSuggestions(false);
    window.location.href = `/properties?search=${encodeURIComponent(suggestion)}`;
  };

  const handleFindHiddenGems = () => {
    window.location.href = '/properties?hiddenGems=true';
  };

  // Sample property data
  const sampleProperties = [
    {
      id: '1',
      address: '123 Brunswick Street',
      suburb: 'New Farm',
      state: 'QLD',
      price: 1250000,
      bedrooms: 3,
      bathrooms: 2,
      parking: 1,
      propertyType: 'Apartment',
      investmentScore: 85,
      developmentScore: 78,
      growthPotential: 12.5,
      isTracked: false
    },
    {
      id: '2',
      address: '456 Gold Coast Highway',
      suburb: 'Surfers Paradise',
      state: 'QLD',
      price: 875000,
      bedrooms: 2,
      bathrooms: 2,
      parking: 1,
      propertyType: 'Apartment',
      investmentScore: 92,
      developmentScore: 65,
      growthPotential: 18.2,
      isTracked: false
    },
    {
      id: '3',
      address: '789 River Road',
      suburb: 'New Farm',
      state: 'QLD',
      price: 650000,
      bedrooms: 4,
      bathrooms: 3,
      parking: 2,
      propertyType: 'House',
      investmentScore: 73,
      developmentScore: 88,
      growthPotential: 9.8,
      isTracked: false
    }
  ];

  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 min-h-screen">

      {/* Hero Section */}
      <section className="relative py-24 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50/50 via-transparent to-indigo-50/50"></div>
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-full h-full max-w-6xl">
          <div className="absolute top-20 left-10 w-72 h-72 bg-primary-100/30 rounded-full blur-3xl"></div>
          <div className="absolute top-40 right-10 w-96 h-96 bg-indigo-100/30 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <div className="inline-flex items-center px-4 py-2 bg-primary-50 border border-primary-200 rounded-full text-primary-700 text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse"></span>
              AI-Powered Property Intelligence Platform
            </div>
          </div>

          <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-8 leading-tight">
            Property Intelligence
            <span className="block bg-gradient-to-r from-primary-600 to-indigo-600 bg-clip-text text-transparent">
              Made Simple
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
            Unlock hidden opportunities with AI-powered property analysis. Get accurate valuations,
            market insights, and development potential powered by 18+ data sources.
          </p>
          
          {/* Enhanced Search Bar */}
          <div className="max-w-3xl mx-auto mb-12">
            <form onSubmit={handleSearch} className="relative">
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-primary-500 to-indigo-500 rounded-2xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
                <div className="relative bg-white rounded-2xl shadow-2xl border border-gray-200/50">
                  <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
                  <input
                    ref={searchInputRef}
                    type="text"
                    value={searchQuery}
                    onChange={(e) => handleInputChange(e.target.value)}
                    onFocus={() => searchQuery.length > 2 && setShowSuggestions(true)}
                    onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                    placeholder="Enter any address, suburb, or postcode..."
                    className="w-full pl-16 pr-40 py-6 text-lg bg-transparent border-0 rounded-2xl focus:ring-0 focus:outline-none placeholder-gray-400"
                  />
                  <button
                    type="submit"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-primary-600 to-indigo-600 hover:from-primary-700 hover:to-indigo-700 text-white font-semibold px-8 py-3 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    Analyze Property
                  </button>
                </div>
              </div>

              {/* Autocomplete Suggestions */}
              {showSuggestions && suggestions.length > 0 && (
                <div className="absolute top-full left-0 right-0 bg-white border border-gray-200/50 rounded-2xl shadow-2xl mt-2 z-50 overflow-hidden backdrop-blur-sm">
                  <div className="p-2">
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="w-full text-left px-4 py-3 hover:bg-primary-50 rounded-xl flex items-center transition-colors group"
                      >
                        <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary-200 transition-colors">
                          <MapPin className="w-4 h-4 text-primary-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{suggestion}</div>
                          <div className="text-sm text-gray-500">Property analysis available</div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </form>

            {/* Quick Action Buttons */}
            <div className="flex flex-wrap justify-center gap-4 mt-8">
              <button
                onClick={handleFindHiddenGems}
                className="group relative overflow-hidden bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold px-6 py-3 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative flex items-center">
                  <Sparkles className="w-5 h-5 mr-2" />
                  Find Hidden Gems
                </div>
              </button>
              <button
                onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
                className="group bg-white hover:bg-gray-50 text-gray-700 hover:text-primary-600 font-semibold px-6 py-3 rounded-xl border border-gray-200 hover:border-primary-300 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <div className="flex items-center">
                  <Filter className="w-5 h-5 mr-2 group-hover:text-primary-600" />
                  Advanced Filters
                </div>
              </button>
              <Link
                href="/properties"
                className="group bg-white hover:bg-gray-50 text-gray-700 hover:text-primary-600 font-semibold px-6 py-3 rounded-xl border border-gray-200 hover:border-primary-300 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <div className="flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2 group-hover:text-primary-600" />
                  Browse Properties
                </div>
              </Link>
            </div>

            {/* Advanced Search Panel */}
            {showAdvancedSearch && (
              <div className="mt-8 p-8 bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-200/50 animate-slide-down">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900">Advanced Filters</h3>
                  <div className="text-sm text-gray-500">Refine your property search</div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Property Type</label>
                    <select className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 hover:bg-white transition-colors">
                      <option value="">All Types</option>
                      <option value="house">House</option>
                      <option value="apartment">Apartment</option>
                      <option value="townhouse">Townhouse</option>
                      <option value="unit">Unit</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Price Range</label>
                    <div className="flex space-x-3">
                      <input
                        type="number"
                        placeholder="Min Price"
                        className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 hover:bg-white transition-colors"
                      />
                      <input
                        type="number"
                        placeholder="Max Price"
                        className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 hover:bg-white transition-colors"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Bedrooms</label>
                    <select className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 hover:bg-white transition-colors">
                      <option value="">Any</option>
                      <option value="1">1+</option>
                      <option value="2">2+</option>
                      <option value="3">3+</option>
                      <option value="4">4+</option>
                      <option value="5">5+</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Development Score</label>
                    <select className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 hover:bg-white transition-colors">
                      <option value="">Any Score</option>
                      <option value="high">High (80-100)</option>
                      <option value="medium">Medium (60-79)</option>
                      <option value="low">Low (0-59)</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Investment Score</label>
                    <select className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 hover:bg-white transition-colors">
                      <option value="">Any Score</option>
                      <option value="high">High (80-100)</option>
                      <option value="medium">Medium (60-79)</option>
                      <option value="low">Low (0-59)</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-800">Risk Level</label>
                    <select className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 hover:bg-white transition-colors">
                      <option value="">Any Risk</option>
                      <option value="low">Low Risk</option>
                      <option value="medium">Medium Risk</option>
                      <option value="high">High Risk</option>
                    </select>
                  </div>
                </div>
                <div className="mt-8 flex justify-end space-x-4">
                  <button
                    onClick={() => setShowAdvancedSearch(false)}
                    className="px-6 py-3 text-gray-700 hover:text-gray-900 font-semibold rounded-xl border border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50 transition-all duration-200"
                  >
                    Cancel
                  </button>
                  <button className="px-8 py-3 bg-gradient-to-r from-primary-600 to-indigo-600 hover:from-primary-700 hover:to-indigo-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
                    Apply Filters
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <div className="text-center group">
              <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 hover:border-primary-200 transition-all duration-300 hover:shadow-xl">
                <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                  10K+
                </div>
                <div className="text-gray-600 font-medium">Properties Analyzed</div>
                <div className="text-sm text-gray-500 mt-1">Comprehensive AI analysis</div>
              </div>
            </div>
            <div className="text-center group">
              <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 hover:border-primary-200 transition-all duration-300 hover:shadow-xl">
                <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                  500+
                </div>
                <div className="text-gray-600 font-medium">Suburbs Covered</div>
                <div className="text-sm text-gray-500 mt-1">Across Australia</div>
              </div>
            </div>
            <div className="text-center group">
              <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 hover:border-primary-200 transition-all duration-300 hover:shadow-xl">
                <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                  95%
                </div>
                <div className="text-gray-600 font-medium">Accuracy Rate</div>
                <div className="text-sm text-gray-500 mt-1">Validated predictions</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Sample Property Cards Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              See Revalu in Action
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore sample properties to see how our AI-powered intelligence provides
              insights you won't find anywhere else.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {sampleProperties.map((property) => (
              <PropertyCard
                key={property.id}
                property={property}
                onTrack={(id) => console.log('Track property:', id)}
                onCompare={(id) => console.log('Compare property:', id)}
              />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link href="/properties" className="btn-primary text-lg px-8 py-3">
              Explore All Properties
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need for Property Intelligence
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our comprehensive platform provides all the tools and insights you need 
              to make informed property decisions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Calculator className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Accurate Valuations</h3>
              <p className="text-gray-600">
                Get precise property valuations using advanced ML algorithms and comprehensive market data.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Market Trends</h3>
              <p className="text-gray-600">
                Track market trends, price movements, and investment opportunities in real-time.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <MapPin className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Suburb Analytics</h3>
              <p className="text-gray-600">
                Comprehensive suburb profiles with demographics, amenities, and growth potential.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Expert Insights</h3>
              <p className="text-gray-600">
                Access professional insights and recommendations from real estate experts.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Reliable Data</h3>
              <p className="text-gray-600">
                Trust in our verified data sources and transparent methodology for all insights.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Search className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Advanced Search</h3>
              <p className="text-gray-600">
                Find properties with powerful filters and intelligent search capabilities.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24 bg-gradient-to-br from-primary-600 via-primary-700 to-indigo-700 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-white/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-indigo-400/20 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="mb-8">
            <div className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full text-white text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
              Join 10,000+ Property Professionals
            </div>
          </div>

          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Ready to Unlock
            <span className="block">Property Intelligence?</span>
          </h2>

          <p className="text-xl md:text-2xl text-primary-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Start your journey with AI-powered property analysis. Get instant access to market insights,
            development potential, and investment opportunities.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              href="/auth/register"
              className="group relative overflow-hidden bg-white text-primary-600 hover:text-primary-700 font-bold px-10 py-4 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-white/25 transform hover:-translate-y-1"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white to-gray-50 opacity-0 group-hover:opacity-100 transition-opacity"></div>
              <span className="relative text-lg">Start Free Trial</span>
            </Link>
            <Link
              href="/properties"
              className="group bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white font-bold px-10 py-4 rounded-2xl border-2 border-white/30 hover:border-white/50 transition-all duration-300 transform hover:-translate-y-1"
            >
              <span className="text-lg">Explore Properties</span>
            </Link>
          </div>

          <div className="mt-12 text-primary-200 text-sm">
            No credit card required • 14-day free trial • Cancel anytime
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">R</span>
                </div>
                <div className="text-3xl font-bold bg-gradient-to-r from-primary-400 to-indigo-400 bg-clip-text text-transparent">
                  Revalu
                </div>
              </div>
              <p className="text-gray-400 text-lg leading-relaxed max-w-md">
                AI-powered property intelligence platform that transforms how you analyze,
                invest, and develop real estate opportunities across Australia.
              </p>
              <div className="mt-6 flex items-center space-x-2 text-sm text-gray-500">
                <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                <span>Live data from 18+ sources</span>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-bold mb-6 text-white">Platform</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/properties" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Properties
                  </Link>
                </li>
                <li>
                  <Link href="/development" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Development Analysis
                  </Link>
                </li>
                <li>
                  <Link href="/market" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Market Intelligence
                  </Link>
                </li>
                <li>
                  <Link href="/valuations" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Valuations
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-bold mb-6 text-white">Support</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/help" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="/docs" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Documentation
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="text-gray-400 hover:text-primary-400 transition-colors flex items-center group">
                    <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    Privacy Policy
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm">
                &copy; 2024 Revalu. All rights reserved. Built with ❤️ for property professionals.
              </p>
              <div className="mt-4 md:mt-0 text-sm text-gray-500">
                Made in Australia 🇦🇺
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
