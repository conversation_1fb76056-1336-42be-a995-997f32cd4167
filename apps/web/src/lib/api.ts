import {
    AuthResponse,
    CreateValuationRequest,
    LoginRequest,
    PaginatedResponse,
    Property,
    PropertySearchParams,
    RegisterRequest,
    Suburb,
    SuburbSearchParams,
    User,
    Valuation
} from '@/types/api';
import axios, { AxiosInstance } from 'axios';
import Cookies from 'js-cookie';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = Cookies.get('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Clear auth token on 401
          Cookies.remove('auth_token');
          // Redirect to login if not already there
          if (typeof window !== 'undefined' && !window.location.pathname.includes('/auth')) {
            window.location.href = '/auth/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await this.client.post<AuthResponse>('/api/v1/auth/login', credentials);
    return response.data;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await this.client.post<AuthResponse>('/api/v1/auth/register', userData);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<User>('/api/v1/auth/me');
    return response.data;
  }

  async logout(): Promise<void> {
    // Clear local token
    Cookies.remove('auth_token');
    // Could also call a logout endpoint if you have one
  }

  async forgotPassword(email: string): Promise<any> {
    const response = await this.client.post('/api/v1/auth/forgot-password', {
      email,
    });
    return response.data;
  }

  async resetPassword(token: string, newPassword: string): Promise<any> {
    const response = await this.client.post('/api/v1/auth/reset-password', {
      token,
      newPassword,
    });
    return response.data;
  }

  // Property endpoints
  async getProperties(params?: PropertySearchParams): Promise<PaginatedResponse<Property>> {
    const response = await this.client.get<PaginatedResponse<Property>>('/api/v1/properties', {
      params,
    });
    return response.data;
  }

  async getProperty(id: string): Promise<Property> {
    const response = await this.client.get<Property>(`/api/v1/properties/${id}`);
    return response.data;
  }

  async getPropertyStatistics(): Promise<any> {
    const response = await this.client.get('/api/v1/properties/statistics');
    return response.data;
  }

  async getNearbyAmenities(id: string): Promise<any> {
    const response = await this.client.get(`/api/v1/properties/${id}/amenities`);
    return response.data;
  }

  // Tracked properties endpoints (mock implementation until backend is ready)
  async trackProperty(propertyId: string): Promise<any> {
    try {
      const response = await this.client.post('/api/v1/tracked-properties', { propertyId });
      return response.data;
    } catch (error: any) {
      // Mock implementation for development
      if (error.response?.status === 404) {
        console.warn('Tracked properties API not implemented yet, using mock');
        return { success: true, message: 'Property tracked (mock)' };
      }
      throw error;
    }
  }

  async untrackProperty(propertyId: string): Promise<any> {
    try {
      const response = await this.client.delete(`/api/v1/tracked-properties/${propertyId}`);
      return response.data;
    } catch (error: any) {
      // Mock implementation for development
      if (error.response?.status === 404) {
        console.warn('Tracked properties API not implemented yet, using mock');
        return { success: true, message: 'Property untracked (mock)' };
      }
      throw error;
    }
  }

  async getTrackedProperties(): Promise<any> {
    try {
      const response = await this.client.get('/api/v1/tracked-properties');
      return response.data;
    } catch (error: any) {
      // Mock implementation for development - return empty array
      if (error.response?.status === 404) {
        console.warn('Tracked properties API not implemented yet, using mock');
        return [];
      }
      throw error;
    }
  }

  // Legacy methods for backward compatibility (deprecated)
  async saveProperty(propertyId: string): Promise<any> {
    console.warn('saveProperty is deprecated, use trackProperty instead');
    return this.trackProperty(propertyId);
  }

  async unsaveProperty(propertyId: string): Promise<any> {
    console.warn('unsaveProperty is deprecated, use untrackProperty instead');
    return this.untrackProperty(propertyId);
  }

  async getSavedProperties(): Promise<any> {
    console.warn('getSavedProperties is deprecated, use getTrackedProperties instead');
    return this.getTrackedProperties();
  }

  // Valuation endpoints
  async getValuations(params?: any): Promise<PaginatedResponse<Valuation>> {
    const response = await this.client.get<PaginatedResponse<Valuation>>('/api/v1/valuations', {
      params,
    });
    return response.data;
  }

  async getValuation(id: string): Promise<Valuation> {
    const response = await this.client.get<Valuation>(`/api/v1/valuations/${id}`);
    return response.data;
  }

  async createValuation(data: CreateValuationRequest): Promise<Valuation> {
    const response = await this.client.post<Valuation>('/api/v1/valuations', data);
    return response.data;
  }

  async getValuationHistory(propertyId: string): Promise<any> {
    const response = await this.client.get(`/api/v1/valuations/property/${propertyId}/history`);
    return response.data;
  }

  // Suburb endpoints
  async getSuburbs(params?: SuburbSearchParams): Promise<PaginatedResponse<Suburb>> {
    const response = await this.client.get<PaginatedResponse<Suburb>>('/api/v1/suburbs', {
      params,
    });
    return response.data;
  }

  async getSuburb(id: string): Promise<Suburb> {
    const response = await this.client.get<Suburb>(`/api/v1/suburbs/${id}`);
    return response.data;
  }

  async getTopSuburbs(criteria: string = 'overall'): Promise<Suburb[]> {
    const response = await this.client.get<Suburb[]>('/api/v1/suburbs/top', {
      params: { criteria },
    });
    return response.data;
  }

  async getSuburbStatistics(id: string): Promise<any> {
    const response = await this.client.get(`/api/v1/suburbs/${id}/statistics`);
    return response.data;
  }

  async getMarketTrends(id: string): Promise<any> {
    const response = await this.client.get(`/api/v1/suburbs/${id}/market-trends`);
    return response.data;
  }

  async compareSuburbs(suburbIds: string[]): Promise<any> {
    const response = await this.client.post('/api/v1/suburbs/compare', { suburbIds });
    return response.data;
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();
export default apiClient;
