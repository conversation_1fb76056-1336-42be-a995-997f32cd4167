'use client';

import { useAuth } from '@/lib/auth';
import { Menu, X } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

export function Header() {
  const { isAuthenticated, user, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  const isActive = (path: string) => pathname === path;

  const handleLogout = () => {
    logout();
    setIsMobileMenuOpen(false);
  };

  return (
    <header className="bg-white shadow-sm border-b sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="text-2xl font-bold text-primary-600">
              Revalu
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link
              href="/properties"
              className={`transition-colors ${
                isActive('/properties')
                  ? 'text-primary-600 font-medium'
                  : 'text-gray-700 hover:text-primary-600'
              }`}
            >
              Properties
            </Link>
            <Link
              href="/development"
              className={`transition-colors ${
                isActive('/development') || pathname?.startsWith('/development')
                  ? 'text-primary-600 font-medium'
                  : 'text-gray-700 hover:text-primary-600'
              }`}
            >
              Development
            </Link>
            <Link
              href="/amalgamation"
              className={`transition-colors ${
                isActive('/amalgamation') || pathname?.startsWith('/amalgamation')
                  ? 'text-primary-600 font-medium'
                  : 'text-gray-700 hover:text-primary-600'
              }`}
            >
              Amalgamation
            </Link>
            <Link
              href="/market"
              className={`transition-colors ${
                isActive('/market') || pathname?.startsWith('/market')
                  ? 'text-primary-600 font-medium'
                  : 'text-gray-700 hover:text-primary-600'
              }`}
            >
              Market
            </Link>
            <Link
              href="/predictions"
              className={`transition-colors ${
                isActive('/predictions') || pathname?.startsWith('/predictions')
                  ? 'text-primary-600 font-medium'
                  : 'text-gray-700 hover:text-primary-600'
              }`}
            >
              Predictions
            </Link>
            <Link
              href="/suburbs"
              className={`transition-colors ${
                isActive('/suburbs')
                  ? 'text-primary-600 font-medium'
                  : 'text-gray-700 hover:text-primary-600'
              }`}
            >
              Suburbs
            </Link>
            <Link 
              href="/valuations" 
              className={`transition-colors ${
                isActive('/valuations') 
                  ? 'text-primary-600 font-medium' 
                  : 'text-gray-700 hover:text-primary-600'
              }`}
            >
              Valuations
            </Link>
          </nav>

          {/* Desktop Auth */}
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                <Link
                  href="/tracked-properties"
                  className={`transition-colors ${
                    isActive('/tracked-properties')
                      ? 'text-primary-600 font-medium'
                      : 'text-gray-700 hover:text-primary-600'
                  }`}
                >
                  Tracked
                </Link>
                <Link 
                  href="/dashboard" 
                  className={`transition-colors ${
                    isActive('/dashboard') 
                      ? 'text-primary-600 font-medium' 
                      : 'text-gray-700 hover:text-primary-600'
                  }`}
                >
                  Dashboard
                </Link>
                <div className="relative group">
                  <button className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {user?.firstName?.charAt(0) || user?.email?.charAt(0) || '?'}
                  </button>
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <div className="px-4 py-2 text-sm text-gray-700 border-b">
                      {user?.firstName} {user?.lastName}
                    </div>
                    <Link href="/dashboard" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Dashboard
                    </Link>
                    <Link href="/tracked-properties" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Tracked Properties
                    </Link>
                    <button 
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Sign Out
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <>
                <Link 
                  href="/auth/login" 
                  className="text-gray-700 hover:text-primary-600 transition-colors"
                >
                  Login
                </Link>
                <Link href="/auth/register" className="btn-primary">
                  Sign Up
                </Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-primary-600 transition-colors"
            >
              {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <div className="space-y-2">
              <Link
                href="/properties"
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  isActive('/properties')
                    ? 'text-primary-600 bg-primary-50'
                    : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Properties
              </Link>
              <Link
                href="/development"
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  isActive('/development') || pathname?.startsWith('/development')
                    ? 'text-primary-600 bg-primary-50'
                    : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Development
              </Link>
              <Link
                href="/amalgamation"
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  isActive('/amalgamation') || pathname?.startsWith('/amalgamation')
                    ? 'text-primary-600 bg-primary-50'
                    : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Amalgamation
              </Link>
              <Link
                href="/market"
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  isActive('/market') || pathname?.startsWith('/market')
                    ? 'text-primary-600 bg-primary-50'
                    : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Market
              </Link>
              <Link
                href="/predictions"
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  isActive('/predictions') || pathname?.startsWith('/predictions')
                    ? 'text-primary-600 bg-primary-50'
                    : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Predictions
              </Link>
              <Link
                href="/suburbs"
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  isActive('/suburbs')
                    ? 'text-primary-600 bg-primary-50'
                    : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Suburbs
              </Link>
              <Link 
                href="/valuations" 
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  isActive('/valuations') 
                    ? 'text-primary-600 bg-primary-50' 
                    : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Valuations
              </Link>
              
              {isAuthenticated ? (
                <>
                  <div className="border-t border-gray-200 pt-2 mt-2">
                    <Link 
                      href="/dashboard" 
                      className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                        isActive('/dashboard') 
                          ? 'text-primary-600 bg-primary-50' 
                          : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
                      }`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                    <Link
                      href="/tracked-properties"
                      className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                        isActive('/tracked-properties')
                          ? 'text-primary-600 bg-primary-50'
                          : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
                      }`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Tracked Properties
                    </Link>
                    <button 
                      onClick={handleLogout}
                      className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50 transition-colors"
                    >
                      Sign Out
                    </button>
                  </div>
                </>
              ) : (
                <div className="border-t border-gray-200 pt-2 mt-2 space-y-2">
                  <Link 
                    href="/auth/login" 
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50 transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Login
                  </Link>
                  <Link 
                    href="/auth/register" 
                    className="block px-3 py-2 rounded-md text-base font-medium bg-primary-600 text-white hover:bg-primary-700 transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Sign Up
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
